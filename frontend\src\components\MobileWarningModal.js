// MobileWarningModal.js
// Mobile device warning modal component

import { Modal } from './Modal';

export const MobileWarningModal = ({ isOpen, onClose }) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={
        <span>
          📱 Desktop Required
        </span>
      }
    >
      <div className="modal-section">
        <div className="modal-status error">
          <span>⚠️</span>
          <span>
            This VectorShift Pipeline Builder is optimized for desktop use and may not function properly on mobile devices.
          </span>
        </div>
        
        <div className="mobile-warning-content">
          <p>For the best experience, please:</p>
          <ul className="mobile-warning-list">
            <li>Use a desktop or laptop computer</li>
            <li>Ensure your screen width is at least 1024px</li>
            <li>Use a modern browser (Chrome, Firefox, Safari, Edge)</li>
          </ul>
          
          <p className="mobile-warning-note">
            The drag-and-drop interface, node connections, and canvas interactions require precise mouse control that works best on desktop devices.
          </p>
        </div>
      </div>

      <div className="modal-actions">
        <button
          className="modal-button primary"
          onClick={onClose}
        >
          I Understand
        </button>
      </div>
    </Modal>
  );
};
