// draggableNode.js

export const DraggableNode = ({ type, label }) => {
    const onDragStart = (event, nodeType) => {
      const appData = { nodeType }
      event.target.style.cursor = 'grabbing';
      event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));
      event.dataTransfer.effectAllowed = 'move';

      // Create a custom drag image to fix the popup/preview bug
      const dragElement = event.target.cloneNode(true);
      dragElement.style.position = 'absolute';
      dragElement.style.top = '-1000px';
      dragElement.style.left = '-1000px';
      dragElement.style.width = event.target.offsetWidth + 'px';
      dragElement.style.height = event.target.offsetHeight + 'px';
      dragElement.style.transform = 'rotate(2deg) scale(0.95)';
      dragElement.style.opacity = '0.8';
      dragElement.style.pointerEvents = 'none';
      dragElement.style.zIndex = '10000';
      dragElement.style.background = 'linear-gradient(135deg, rgba(138, 43, 226, 0.9) 0%, rgba(79, 70, 229, 0.9) 100%)';
      dragElement.style.border = '2px solid rgba(138, 43, 226, 0.8)';
      dragElement.style.borderRadius = '8px';
      dragElement.style.boxShadow = '0 8px 32px rgba(138, 43, 226, 0.4)';

      document.body.appendChild(dragElement);
      event.dataTransfer.setDragImage(dragElement, event.target.offsetWidth / 2, event.target.offsetHeight / 2);

      // Clean up the drag image after a short delay
      setTimeout(() => {
          if (document.body.contains(dragElement)) {
              document.body.removeChild(dragElement);
          }
      }, 100);
    };

    return (
      <div
        className={`draggable-node ${type}`}
        onDragStart={(event) => onDragStart(event, type)}
        onDragEnd={(event) => (event.target.style.cursor = 'grab')}
        draggable
      >
          <span>{label}</span>
      </div>
    );
  };
  