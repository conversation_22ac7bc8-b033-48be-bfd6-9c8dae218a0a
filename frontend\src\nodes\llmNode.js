// llmNode.js

import { BaseNode, createHandle } from './BaseNode';
import { Position } from 'reactflow';

export const LLMNode = ({ id, data }) => {
  const handles = [
    createHandle(`${id}-system`, 'target', Position.Left, { top: `${100/3}%` }),
    createHandle(`${id}-prompt`, 'target', Position.Left, { top: `${200/3}%` }),
    createHandle(`${id}-response`, 'source', Position.Right)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="LLM"
      handles={handles}
      nodeType="llm"
    >
      <div style={{
        fontSize: '12px',
        color: '#ffffff',
        fontWeight: '500',
        textAlign: 'center',
        opacity: 0.9
      }}>
        <span>Large Language Model</span>
      </div>
    </BaseNode>
  );
}
