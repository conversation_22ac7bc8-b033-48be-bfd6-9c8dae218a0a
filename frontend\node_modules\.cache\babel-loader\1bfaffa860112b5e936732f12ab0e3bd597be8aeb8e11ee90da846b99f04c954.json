{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\nodes\\\\timerNode.js\",\n  _s = $RefreshSig$();\n// timerNode.js\n// Demonstrates a timer/delay node with configurable duration\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, inlineLabelStyle } from './BaseNode';\nimport { CustomDropdown } from '../components/CustomDropdown';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const TimerNode = ({\n  id,\n  data\n}) => {\n  _s();\n  const [duration, setDuration] = useState((data === null || data === void 0 ? void 0 : data.duration) || 1000);\n  const [unit, setUnit] = useState((data === null || data === void 0 ? void 0 : data.unit) || 'ms');\n  const handleDurationChange = e => {\n    setDuration(parseInt(e.target.value) || 0);\n  };\n  const handleUnitChange = e => {\n    setUnit(e.target.value);\n  };\n  const unitOptions = [{\n    value: 'ms',\n    label: 'ms'\n  }, {\n    value: 's',\n    label: 's'\n  }, {\n    value: 'm',\n    label: 'm'\n  }];\n  const handles = [HANDLE_CONFIGS.targetLeft(`${id}-trigger`), HANDLE_CONFIGS.sourceRight(`${id}-delayed`)];\n  return /*#__PURE__*/_jsxDEV(BaseNode, {\n    id: id,\n    data: data,\n    title: \"Timer\",\n    handles: handles,\n    nodeType: \"timer\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600',\n          marginBottom: '8px'\n        },\n        children: [\"Duration:\", /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"number\",\n          value: duration,\n          onChange: handleDurationChange,\n          className: \"node-input\",\n          style: {\n            width: '100%',\n            fontSize: '11px'\n          },\n          min: \"0\",\n          step: \"1\",\n          placeholder: \"Enter duration\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        style: {\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600',\n          marginBottom: '8px'\n        },\n        children: [\"Unit:\", /*#__PURE__*/_jsxDEV(CustomDropdown, {\n          value: unit,\n          onChange: handleUnitChange,\n          options: unitOptions,\n          style: {\n            width: '100%'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        },\n        children: [\"Delays execution by \", duration, \" \", unit]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(TimerNode, \"m+3RnTue3OQ7QWY94W+/xWdhgNE=\");\n_c = TimerNode;\nvar _c;\n$RefreshReg$(_c, \"TimerNode\");", "map": {"version": 3, "names": ["useState", "BaseNode", "HANDLE_CONFIGS", "inlineLabelStyle", "CustomDropdown", "jsxDEV", "_jsxDEV", "TimerNode", "id", "data", "_s", "duration", "setDuration", "unit", "setUnit", "handleDurationChange", "e", "parseInt", "target", "value", "handleUnitChange", "unitOptions", "label", "handles", "targetLeft", "sourceRight", "title", "nodeType", "children", "style", "display", "flexDirection", "gap", "fontSize", "color", "fontWeight", "marginBottom", "type", "onChange", "className", "width", "min", "step", "placeholder", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "options", "fontStyle", "textAlign", "padding", "backgroundColor", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/nodes/timerNode.js"], "sourcesContent": ["// timerNode.js\n// Demonstrates a timer/delay node with configurable duration\n\nimport { useState } from 'react';\nimport { BaseNode, HANDLE_CONFIGS, inlineLabelStyle } from './BaseNode';\nimport { CustomDropdown } from '../components/CustomDropdown';\n\nexport const TimerNode = ({ id, data }) => {\n  const [duration, setDuration] = useState(data?.duration || 1000);\n  const [unit, setUnit] = useState(data?.unit || 'ms');\n\n  const handleDurationChange = (e) => {\n    setDuration(parseInt(e.target.value) || 0);\n  };\n\n  const handleUnitChange = (e) => {\n    setUnit(e.target.value);\n  };\n\n  const unitOptions = [\n    { value: 'ms', label: 'ms' },\n    { value: 's', label: 's' },\n    { value: 'm', label: 'm' }\n  ];\n\n  const handles = [\n    HANDLE_CONFIGS.targetLeft(`${id}-trigger`),\n    HANDLE_CONFIGS.sourceRight(`${id}-delayed`)\n  ];\n\n  return (\n    <BaseNode\n      id={id}\n      data={data}\n      title=\"Timer\"\n      handles={handles}\n      nodeType=\"timer\"\n    >\n      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n        {/* Duration Input */}\n        <label style={{\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600',\n          marginBottom: '8px'\n        }}>\n          Duration:\n          <input\n            type=\"number\"\n            value={duration}\n            onChange={handleDurationChange}\n            className=\"node-input\"\n            style={{\n              width: '100%',\n              fontSize: '11px'\n            }}\n            min=\"0\"\n            step=\"1\"\n            placeholder=\"Enter duration\"\n          />\n        </label>\n\n        {/* Unit Dropdown */}\n        <label style={{\n          fontSize: '12px',\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          color: '#ffffff',\n          fontWeight: '600',\n          marginBottom: '8px'\n        }}>\n          Unit:\n          <CustomDropdown\n            value={unit}\n            onChange={handleUnitChange}\n            options={unitOptions}\n            style={{\n              width: '100%'\n            }}\n          />\n        </label>\n\n        {/* Helper text */}\n        <div style={{\n          fontSize: '10px',\n          color: 'rgba(255, 255, 255, 0.6)',\n          fontStyle: 'italic',\n          textAlign: 'center',\n          padding: '4px 8px',\n          backgroundColor: 'rgba(138, 43, 226, 0.1)',\n          borderRadius: '4px',\n          border: '1px solid rgba(138, 43, 226, 0.2)'\n        }}>\n          Delays execution by {duration} {unit}\n        </div>\n      </div>\n    </BaseNode>\n  );\n};\n"], "mappings": ";;AAAA;AACA;;AAEA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,YAAY;AACvE,SAASC,cAAc,QAAQ,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,OAAO,MAAMC,SAAS,GAAGA,CAAC;EAAEC,EAAE;EAAEC;AAAK,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,QAAQ,KAAI,IAAI,CAAC;EAChE,MAAM,CAACE,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,CAAAS,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,IAAI,KAAI,IAAI,CAAC;EAEpD,MAAME,oBAAoB,GAAIC,CAAC,IAAK;IAClCJ,WAAW,CAACK,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC,IAAI,CAAC,CAAC;EAC5C,CAAC;EAED,MAAMC,gBAAgB,GAAIJ,CAAC,IAAK;IAC9BF,OAAO,CAACE,CAAC,CAACE,MAAM,CAACC,KAAK,CAAC;EACzB,CAAC;EAED,MAAME,WAAW,GAAG,CAClB;IAAEF,KAAK,EAAE,IAAI;IAAEG,KAAK,EAAE;EAAK,CAAC,EAC5B;IAAEH,KAAK,EAAE,GAAG;IAAEG,KAAK,EAAE;EAAI,CAAC,EAC1B;IAAEH,KAAK,EAAE,GAAG;IAAEG,KAAK,EAAE;EAAI,CAAC,CAC3B;EAED,MAAMC,OAAO,GAAG,CACdrB,cAAc,CAACsB,UAAU,CAAE,GAAEhB,EAAG,UAAS,CAAC,EAC1CN,cAAc,CAACuB,WAAW,CAAE,GAAEjB,EAAG,UAAS,CAAC,CAC5C;EAED,oBACEF,OAAA,CAACL,QAAQ;IACPO,EAAE,EAAEA,EAAG;IACPC,IAAI,EAAEA,IAAK;IACXiB,KAAK,EAAC,OAAO;IACbH,OAAO,EAAEA,OAAQ;IACjBI,QAAQ,EAAC,OAAO;IAAAC,QAAA,eAEhBtB,OAAA;MAAKuB,KAAK,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,aAAa,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAJ,QAAA,gBAEnEtB,OAAA;QAAOuB,KAAK,EAAE;UACZI,QAAQ,EAAE,MAAM;UAChBH,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE,KAAK;UACVE,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE;QAChB,CAAE;QAAAR,QAAA,GAAC,WAED,eAAAtB,OAAA;UACE+B,IAAI,EAAC,QAAQ;UACblB,KAAK,EAAER,QAAS;UAChB2B,QAAQ,EAAEvB,oBAAqB;UAC/BwB,SAAS,EAAC,YAAY;UACtBV,KAAK,EAAE;YACLW,KAAK,EAAE,MAAM;YACbP,QAAQ,EAAE;UACZ,CAAE;UACFQ,GAAG,EAAC,GAAG;UACPC,IAAI,EAAC,GAAG;UACRC,WAAW,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGRzC,OAAA;QAAOuB,KAAK,EAAE;UACZI,QAAQ,EAAE,MAAM;UAChBH,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,GAAG,EAAE,KAAK;UACVE,KAAK,EAAE,SAAS;UAChBC,UAAU,EAAE,KAAK;UACjBC,YAAY,EAAE;QAChB,CAAE;QAAAR,QAAA,GAAC,OAED,eAAAtB,OAAA,CAACF,cAAc;UACbe,KAAK,EAAEN,IAAK;UACZyB,QAAQ,EAAElB,gBAAiB;UAC3B4B,OAAO,EAAE3B,WAAY;UACrBQ,KAAK,EAAE;YACLW,KAAK,EAAE;UACT;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGRzC,OAAA;QAAKuB,KAAK,EAAE;UACVI,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,0BAA0B;UACjCe,SAAS,EAAE,QAAQ;UACnBC,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,SAAS;UAClBC,eAAe,EAAE,yBAAyB;UAC1CC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAA1B,QAAA,GAAC,sBACmB,EAACjB,QAAQ,EAAC,GAAC,EAACE,IAAI;MAAA;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEf,CAAC;AAACrC,EAAA,CA/FWH,SAAS;AAAAgD,EAAA,GAAThD,SAAS;AAAA,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}