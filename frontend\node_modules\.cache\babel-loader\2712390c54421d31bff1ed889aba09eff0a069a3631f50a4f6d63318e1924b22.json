{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\components\\\\MobileWarningModal.js\";\n// MobileWarningModal.js\n// Mobile device warning modal component\n\nimport { Modal } from './Modal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const MobileWarningModal = ({\n  isOpen,\n  onClose\n}) => {\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    isOpen: isOpen,\n    onClose: onClose,\n    title: /*#__PURE__*/_jsxDEV(\"span\", {\n      children: \"\\uD83D\\uDCF1 Desktop Required\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 9\n    }, this),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-status error\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\u26A0\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"This VectorShift Pipeline Builder is optimized for desktop use and may not function properly on mobile devices.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-warning-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"For the best experience, please:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"mobile-warning-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Use a desktop or laptop computer\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Ensure your screen width is at least 1024px\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: \"Use a modern browser (Chrome, Firefox, Safari, Edge)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mobile-warning-note\",\n          children: \"The drag-and-drop interface, node connections, and canvas interactions require precise mouse control that works best on desktop devices.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-actions\",\n      children: /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"modal-button primary\",\n        onClick: onClose,\n        children: \"I Understand\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = MobileWarningModal;\nvar _c;\n$RefreshReg$(_c, \"MobileWarningModal\");", "map": {"version": 3, "names": ["Modal", "jsxDEV", "_jsxDEV", "MobileWarningModal", "isOpen", "onClose", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/components/MobileWarningModal.js"], "sourcesContent": ["// MobileWarningModal.js\n// Mobile device warning modal component\n\nimport { Modal } from './Modal';\n\nexport const MobileWarningModal = ({ isOpen, onClose }) => {\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={\n        <span>\n          📱 Desktop Required\n        </span>\n      }\n    >\n      <div className=\"modal-section\">\n        <div className=\"modal-status error\">\n          <span>⚠️</span>\n          <span>\n            This VectorShift Pipeline Builder is optimized for desktop use and may not function properly on mobile devices.\n          </span>\n        </div>\n        \n        <div className=\"mobile-warning-content\">\n          <p>For the best experience, please:</p>\n          <ul className=\"mobile-warning-list\">\n            <li>Use a desktop or laptop computer</li>\n            <li>Ensure your screen width is at least 1024px</li>\n            <li>Use a modern browser (Chrome, Firefox, Safari, Edge)</li>\n          </ul>\n          \n          <p className=\"mobile-warning-note\">\n            The drag-and-drop interface, node connections, and canvas interactions require precise mouse control that works best on desktop devices.\n          </p>\n        </div>\n      </div>\n\n      <div className=\"modal-actions\">\n        <button\n          className=\"modal-button primary\"\n          onClick={onClose}\n        >\n          I Understand\n        </button>\n      </div>\n    </Modal>\n  );\n};\n"], "mappings": ";AAAA;AACA;;AAEA,SAASA,KAAK,QAAQ,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,OAAO,MAAMC,kBAAkB,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAQ,CAAC,KAAK;EACzD,oBACEH,OAAA,CAACF,KAAK;IACJI,MAAM,EAAEA,MAAO;IACfC,OAAO,EAAEA,OAAQ;IACjBC,KAAK,eACHJ,OAAA;MAAAK,QAAA,EAAM;IAEN;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CACP;IAAAJ,QAAA,gBAEDL,OAAA;MAAKU,SAAS,EAAC,eAAe;MAAAL,QAAA,gBAC5BL,OAAA;QAAKU,SAAS,EAAC,oBAAoB;QAAAL,QAAA,gBACjCL,OAAA;UAAAK,QAAA,EAAM;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACfT,OAAA;UAAAK,QAAA,EAAM;QAEN;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENT,OAAA;QAAKU,SAAS,EAAC,wBAAwB;QAAAL,QAAA,gBACrCL,OAAA;UAAAK,QAAA,EAAG;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACvCT,OAAA;UAAIU,SAAS,EAAC,qBAAqB;UAAAL,QAAA,gBACjCL,OAAA;YAAAK,QAAA,EAAI;UAAgC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCT,OAAA;YAAAK,QAAA,EAAI;UAA2C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpDT,OAAA;YAAAK,QAAA,EAAI;UAAoD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC,eAELT,OAAA;UAAGU,SAAS,EAAC,qBAAqB;UAAAL,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENT,OAAA;MAAKU,SAAS,EAAC,eAAe;MAAAL,QAAA,eAC5BL,OAAA;QACEU,SAAS,EAAC,sBAAsB;QAChCC,OAAO,EAAER,OAAQ;QAAAE,QAAA,EAClB;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACG,EAAA,GA3CWX,kBAAkB;AAAA,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}