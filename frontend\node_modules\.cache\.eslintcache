[{"D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js": "1", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js": "2", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js": "3", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js": "4", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js": "5", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js": "6", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js": "7", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js": "8", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js": "9", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js": "10", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js": "11", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js": "12", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js": "13", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js": "14", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js": "15", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js": "16", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js": "17", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js": "18", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js": "19", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js": "20", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\CustomDropdown.js": "21", "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\MobileWarningModal.js": "22"}, {"size": 254, "mtime": 1750274986338, "results": "23", "hashOfConfig": "24"}, {"size": 1530, "mtime": 1750425743666, "results": "25", "hashOfConfig": "24"}, {"size": 5160, "mtime": 1750425794237, "results": "26", "hashOfConfig": "24"}, {"size": 6877, "mtime": 1750428327312, "results": "27", "hashOfConfig": "24"}, {"size": 3945, "mtime": 1750430609993, "results": "28", "hashOfConfig": "24"}, {"size": 621, "mtime": 1750285171720, "results": "29", "hashOfConfig": "24"}, {"size": 1353, "mtime": 1750285195771, "results": "30", "hashOfConfig": "24"}, {"size": 1460, "mtime": 1750369055789, "results": "31", "hashOfConfig": "24"}, {"size": 1473, "mtime": 1750369067200, "results": "32", "hashOfConfig": "24"}, {"size": 6554, "mtime": 1750368221041, "results": "33", "hashOfConfig": "24"}, {"size": 762, "mtime": 1750352626197, "results": "34", "hashOfConfig": "24"}, {"size": 5286, "mtime": 1750368201849, "results": "35", "hashOfConfig": "24"}, {"size": 1429, "mtime": 1750369032589, "results": "36", "hashOfConfig": "24"}, {"size": 1789, "mtime": 1750368999597, "results": "37", "hashOfConfig": "24"}, {"size": 1786, "mtime": 1750369011610, "results": "38", "hashOfConfig": "24"}, {"size": 2530, "mtime": 1750369022075, "results": "39", "hashOfConfig": "24"}, {"size": 2654, "mtime": 1750423184736, "results": "40", "hashOfConfig": "24"}, {"size": 2138, "mtime": 1750279121739, "results": "41", "hashOfConfig": "24"}, {"size": 718, "mtime": 1750286504985, "results": "42", "hashOfConfig": "24"}, {"size": 3508, "mtime": 1750314892016, "results": "43", "hashOfConfig": "24"}, {"size": 3820, "mtime": 1750368381812, "results": "44", "hashOfConfig": "24"}, {"size": 1432, "mtime": 1750425758777, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, "159wf7k", {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "65"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "49"}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\index.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\App.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\toolbar.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\ui.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\submit.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\draggableNode.js", [], [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\store.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\inputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\outputNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\textNode.js", ["114"], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\llmNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\BaseNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\switchNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\mathNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\filterNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\aggregatorNode.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\nodes\\timerNode.js", ["115"], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\styles\\theme.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\NodeInput.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\Modal.js", [], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\CustomDropdown.js", ["116", "117"], [], "D:\\Job Assesment\\frontend_technical_assessment\\frontend\\src\\components\\MobileWarningModal.js", [], [], {"ruleId": "118", "severity": 1, "message": "119", "line": 71, "column": 6, "nodeType": "120", "endLine": 71, "endColumn": 34, "suggestions": "121"}, {"ruleId": "122", "severity": 1, "message": "123", "line": 5, "column": 36, "nodeType": "124", "messageId": "125", "endLine": 5, "endColumn": 52}, {"ruleId": "126", "severity": 1, "message": "127", "line": 38, "column": 7, "nodeType": "128", "messageId": "129", "endLine": 61, "endColumn": 8}, {"ruleId": "118", "severity": 1, "message": "130", "line": 68, "column": 6, "nodeType": "120", "endLine": 68, "endColumn": 37, "suggestions": "131"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'calculateSize'. Either include it or remove the dependency array.", "ArrayExpression", ["132"], "no-unused-vars", "'inlineLabelStyle' is defined but never used.", "Identifier", "unusedVar", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "React Hook useEffect has a missing dependency: 'handleOptionSelect'. Either include it or remove the dependency array.", ["133"], {"desc": "134", "fix": "135"}, {"desc": "136", "fix": "137"}, "Update the dependencies array to be: [calculateSize, currText, variables.length]", {"range": "138", "text": "139"}, "Update the dependencies array to be: [isOpen, focusedIndex, options, handleOptionSelect]", {"range": "140", "text": "141"}, [2432, 2460], "[calculateSize, currText, variables.length]", [1852, 1883], "[isOpen, focusedIndex, options, handleOptionSelect]"]