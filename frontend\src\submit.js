// submit.js

import { useState } from 'react';
import { useStore } from './store';
import { shallow } from 'zustand/shallow';
import { PipelineAnalysisModal, ErrorModal } from './components/Modal';

const selector = (state) => ({
  nodes: state.nodes,
  edges: state.edges,
});

export const SubmitButton = () => {
    const { nodes, edges } = useStore(selector, shallow);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [analysisResult, setAnalysisResult] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isErrorModalOpen, setIsErrorModalOpen] = useState(false);
    const [errorMessage, setErrorMessage] = useState(null);

    const handleSubmit = async () => {
        setIsLoading(true);
        try {
            // Prepare pipeline data
            const pipelineData = {
                nodes: nodes,
                edges: edges
            };

            // Send to backend
            const response = await fetch('http://localhost:8000/pipelines/parse', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(pipelineData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();

            // Show modal with results
            setAnalysisResult(result);
            setIsModalOpen(true);

        } catch (error) {
            console.error('Error submitting pipeline:', error);
            // Use consistent modal-based error handling
            setErrorMessage(`Error submitting pipeline: ${error.message}`);
            setIsErrorModalOpen(true);
        } finally {
            setIsLoading(false);
        }
    };

    const closeModal = () => {
        setIsModalOpen(false);
        setAnalysisResult(null);
    };

    const closeErrorModal = () => {
        setIsErrorModalOpen(false);
        setErrorMessage(null);
    };

    const hasNodes = nodes.length > 0;

    return (
        <>
            <div className="submit-container">
                <div className="submit-info-horizontal">
                    <div className="pipeline-stats">
                        <span className="stat-item">
                            <span className="stat-number">{nodes.length}</span>
                            <span className="stat-label">Nodes</span>
                        </span>
                        <span className="stat-divider">•</span>
                        <span className="stat-item">
                            <span className="stat-number">{edges.length}</span>
                            <span className="stat-label">Connections</span>
                        </span>
                    </div>
                    <button
                        type="button"
                        className={`submit-button ${!hasNodes || isLoading ? 'disabled' : ''}`}
                        onClick={handleSubmit}
                        disabled={!hasNodes || isLoading}
                    >
                        {isLoading ? (
                            <span style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                <div className="loading-spinner"></div>
                                Analyzing...
                            </span>
                        ) : hasNodes ? 'Analyze Pipeline' : 'Build Your Pipeline'}
                    </button>
                </div>
            </div>

            <PipelineAnalysisModal
                isOpen={isModalOpen}
                onClose={closeModal}
                analysisResult={analysisResult}
            />

            <ErrorModal
                isOpen={isErrorModalOpen}
                onClose={closeErrorModal}
                errorMessage={errorMessage}
            />
        </>
    );
}
