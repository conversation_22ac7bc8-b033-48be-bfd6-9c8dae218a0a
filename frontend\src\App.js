import { useState, useEffect } from 'react';
import { PipelineToolbar } from './toolbar';
import { PipelineUI } from './ui';
import { SubmitButton } from './submit';
import { MobileWarningModal } from './components/MobileWarningModal';

// Debug logging for image/resource loading issues
console.log('App.js loaded successfully');

// Check if fonts are loading
document.fonts.ready.then(() => {
  console.log('Fonts loaded successfully');
}).catch((error) => {
  console.error('Font loading error:', error);
});

function App() {
  const [showMobileWarning, setShowMobileWarning] = useState(false);

  useEffect(() => {
    const checkMobileDevice = () => {
      const isMobile = window.innerWidth <= 768 ||
                      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      if (isMobile && !localStorage.getItem('mobileWarningDismissed')) {
        setShowMobileWarning(true);
      }
    };

    checkMobileDevice();
    window.addEventListener('resize', checkMobileDevice);

    return () => window.removeEventListener('resize', checkMobileDevice);
  }, []);

  const handleMobileWarningClose = () => {
    setShowMobileWarning(false);
    localStorage.setItem('mobileWarningDismissed', 'true');
  };

  return (
    <div className="app-container">
      <PipelineToolbar />
      <PipelineUI />
      <SubmitButton />
      <MobileWarningModal
        isOpen={showMobileWarning}
        onClose={handleMobileWarningClose}
      />
    </div>
  );
}

export default App;
