// timerNode.js
// Demonstrates a timer/delay node with configurable duration

import { useState } from 'react';
import { BaseNode, HANDLE_CONFIGS, inlineLabelStyle } from './BaseNode';
import { CustomDropdown } from '../components/CustomDropdown';

export const TimerNode = ({ id, data }) => {
  const [duration, setDuration] = useState(data?.duration || 1000);
  const [unit, setUnit] = useState(data?.unit || 'ms');

  const handleDurationChange = (e) => {
    setDuration(parseInt(e.target.value) || 0);
  };

  const handleUnitChange = (e) => {
    setUnit(e.target.value);
  };

  const unitOptions = [
    { value: 'ms', label: 'ms' },
    { value: 's', label: 's' },
    { value: 'm', label: 'm' }
  ];

  const handles = [
    HANDLE_CONFIGS.targetLeft(`${id}-trigger`),
    HANDLE_CONFIGS.sourceRight(`${id}-delayed`)
  ];

  return (
    <BaseNode
      id={id}
      data={data}
      title="Timer"
      handles={handles}
      nodeType="timer"
    >
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        {/* Duration Input */}
        <label style={{
          fontSize: '12px',
          display: 'flex',
          flexDirection: 'column',
          gap: '4px',
          color: '#ffffff',
          fontWeight: '600',
          marginBottom: '8px'
        }}>
          Duration:
          <input
            type="number"
            value={duration}
            onChange={handleDurationChange}
            className="node-input"
            style={{
              width: '100%',
              fontSize: '11px'
            }}
            min="0"
            step="1"
            placeholder="Enter duration"
          />
        </label>

        {/* Unit Dropdown */}
        <label style={{
          fontSize: '12px',
          display: 'flex',
          flexDirection: 'column',
          gap: '4px',
          color: '#ffffff',
          fontWeight: '600',
          marginBottom: '8px'
        }}>
          Unit:
          <CustomDropdown
            value={unit}
            onChange={handleUnitChange}
            options={unitOptions}
            style={{
              width: '100%'
            }}
          />
        </label>

        {/* Helper text */}
        <div style={{
          fontSize: '10px',
          color: 'rgba(255, 255, 255, 0.6)',
          fontStyle: 'italic',
          textAlign: 'center',
          padding: '4px 8px',
          backgroundColor: 'rgba(138, 43, 226, 0.1)',
          borderRadius: '4px',
          border: '1px solid rgba(138, 43, 226, 0.2)'
        }}>
          Delays execution by {duration} {unit}
        </div>
      </div>
    </BaseNode>
  );
};
