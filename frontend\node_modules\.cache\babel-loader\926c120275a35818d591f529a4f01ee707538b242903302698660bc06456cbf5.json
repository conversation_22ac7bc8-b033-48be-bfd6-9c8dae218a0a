{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { useState, useEffect } from 'react';\nimport { PipelineToolbar } from './toolbar';\nimport { PipelineUI } from './ui';\nimport { SubmitButton } from './submit';\nimport { MobileWarningModal } from './components/MobileWarningModal';\n\n// Debug logging for image/resource loading issues\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconsole.log('App.js loaded successfully');\n\n// Check if fonts are loading\ndocument.fonts.ready.then(() => {\n  console.log('Fonts loaded successfully');\n}).catch(error => {\n  console.error('Font loading error:', error);\n});\nfunction App() {\n  _s();\n  const [showMobileWarning, setShowMobileWarning] = useState(false);\n  useEffect(() => {\n    const checkMobileDevice = () => {\n      const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n      if (isMobile && !localStorage.getItem('mobileWarningDismissed')) {\n        setShowMobileWarning(true);\n      }\n    };\n    checkMobileDevice();\n    window.addEventListener('resize', checkMobileDevice);\n    return () => window.removeEventListener('resize', checkMobileDevice);\n  }, []);\n  const handleMobileWarningClose = () => {\n    setShowMobileWarning(false);\n    localStorage.setItem('mobileWarningDismissed', 'true');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(PipelineToolbar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PipelineUI, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SubmitButton, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(MobileWarningModal, {\n      isOpen: showMobileWarning,\n      onClose: handleMobileWarningClose\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"rTNGJ+tUjSQnAj1fi6H22AkehAg=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "useEffect", "PipelineToolbar", "PipelineUI", "SubmitButton", "MobileWarningModal", "jsxDEV", "_jsxDEV", "console", "log", "document", "fonts", "ready", "then", "catch", "error", "App", "_s", "showMobileWarning", "setShowMobileWarning", "checkMobileDevice", "isMobile", "window", "innerWidth", "test", "navigator", "userAgent", "localStorage", "getItem", "addEventListener", "removeEventListener", "handleMobileWarningClose", "setItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isOpen", "onClose", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/App.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { PipelineToolbar } from './toolbar';\nimport { PipelineUI } from './ui';\nimport { SubmitButton } from './submit';\nimport { MobileWarningModal } from './components/MobileWarningModal';\n\n// Debug logging for image/resource loading issues\nconsole.log('App.js loaded successfully');\n\n// Check if fonts are loading\ndocument.fonts.ready.then(() => {\n  console.log('Fonts loaded successfully');\n}).catch((error) => {\n  console.error('Font loading error:', error);\n});\n\nfunction App() {\n  const [showMobileWarning, setShowMobileWarning] = useState(false);\n\n  useEffect(() => {\n    const checkMobileDevice = () => {\n      const isMobile = window.innerWidth <= 768 ||\n                      /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);\n\n      if (isMobile && !localStorage.getItem('mobileWarningDismissed')) {\n        setShowMobileWarning(true);\n      }\n    };\n\n    checkMobileDevice();\n    window.addEventListener('resize', checkMobileDevice);\n\n    return () => window.removeEventListener('resize', checkMobileDevice);\n  }, []);\n\n  const handleMobileWarningClose = () => {\n    setShowMobileWarning(false);\n    localStorage.setItem('mobileWarningDismissed', 'true');\n  };\n\n  return (\n    <div className=\"app-container\">\n      <PipelineToolbar />\n      <PipelineUI />\n      <SubmitButton />\n      <MobileWarningModal\n        isOpen={showMobileWarning}\n        onClose={handleMobileWarningClose}\n      />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC3C,SAASC,eAAe,QAAQ,WAAW;AAC3C,SAASC,UAAU,QAAQ,MAAM;AACjC,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,kBAAkB,QAAQ,iCAAiC;;AAEpE;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACAC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;;AAEzC;AACAC,QAAQ,CAACC,KAAK,CAACC,KAAK,CAACC,IAAI,CAAC,MAAM;EAC9BL,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;AAC1C,CAAC,CAAC,CAACK,KAAK,CAAEC,KAAK,IAAK;EAClBP,OAAO,CAACO,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;AAC7C,CAAC,CAAC;AAEF,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAEjEC,SAAS,CAAC,MAAM;IACd,MAAMmB,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,MAAMC,QAAQ,GAAGC,MAAM,CAACC,UAAU,IAAI,GAAG,IACzB,gEAAgE,CAACC,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC;MAE1G,IAAIL,QAAQ,IAAI,CAACM,YAAY,CAACC,OAAO,CAAC,wBAAwB,CAAC,EAAE;QAC/DT,oBAAoB,CAAC,IAAI,CAAC;MAC5B;IACF,CAAC;IAEDC,iBAAiB,CAAC,CAAC;IACnBE,MAAM,CAACO,gBAAgB,CAAC,QAAQ,EAAET,iBAAiB,CAAC;IAEpD,OAAO,MAAME,MAAM,CAACQ,mBAAmB,CAAC,QAAQ,EAAEV,iBAAiB,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMW,wBAAwB,GAAGA,CAAA,KAAM;IACrCZ,oBAAoB,CAAC,KAAK,CAAC;IAC3BQ,YAAY,CAACK,OAAO,CAAC,wBAAwB,EAAE,MAAM,CAAC;EACxD,CAAC;EAED,oBACEzB,OAAA;IAAK0B,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5B3B,OAAA,CAACL,eAAe;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACnB/B,OAAA,CAACJ,UAAU;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACd/B,OAAA,CAACH,YAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChB/B,OAAA,CAACF,kBAAkB;MACjBkC,MAAM,EAAErB,iBAAkB;MAC1BsB,OAAO,EAAET;IAAyB;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACrB,EAAA,CAnCQD,GAAG;AAAAyB,EAAA,GAAHzB,GAAG;AAqCZ,eAAeA,GAAG;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}