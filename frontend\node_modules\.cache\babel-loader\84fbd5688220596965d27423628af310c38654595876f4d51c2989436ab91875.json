{"ast": null, "code": "var _jsxFileName = \"D:\\\\Job Assesment\\\\frontend_technical_assessment\\\\frontend\\\\src\\\\toolbar.js\",\n  _s = $RefreshSig$();\n// toolbar.js\nimport { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const PipelineToolbar = () => {\n  _s();\n  const [screenWidth, setScreenWidth] = useState(window.innerWidth);\n  useEffect(() => {\n    const handleResize = () => setScreenWidth(window.innerWidth);\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const getResponsiveTitle = () => {\n    if (screenWidth <= 360) return 'VectorShift';\n    if (screenWidth <= 480) return 'VectorShift Builder';\n    return 'VectorShift Pipeline Builder';\n  };\n  const onDragStart = (event, nodeType) => {\n    const appData = {\n      nodeType\n    };\n    event.target.style.cursor = 'grabbing';\n    event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n    event.dataTransfer.effectAllowed = 'move';\n  };\n  const onDragEnd = event => {\n    event.target.style.cursor = 'grab';\n  };\n  const nodeTypes = [{\n    type: 'customInput',\n    label: 'Input',\n    color: '#8b5cf6'\n  }, {\n    type: 'llm',\n    label: 'LLM',\n    color: '#6366f1'\n  }, {\n    type: 'customOutput',\n    label: 'Output',\n    color: '#8b5cf6'\n  }, {\n    type: 'text',\n    label: 'Text',\n    color: '#10b981'\n  }, {\n    type: 'math',\n    label: 'Math',\n    color: '#3b82f6'\n  }, {\n    type: 'filter',\n    label: 'Filter',\n    color: '#f59e0b'\n  }, {\n    type: 'timer',\n    label: 'Timer',\n    color: '#ef4444'\n  }, {\n    type: 'switch',\n    label: 'Switch',\n    color: '#06b6d4'\n  }, {\n    type: 'aggregator',\n    label: 'Aggregator',\n    color: '#ec4899'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pipeline-node-toolbar\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"node-toolbar-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-title\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          children: getResponsiveTitle()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"toolbar-nodes\",\n        children: nodeTypes.map(node => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"draggable-node\",\n          draggable: true,\n          onDragStart: event => onDragStart(event, node.type),\n          onDragEnd: onDragEnd,\n          style: {\n            cursor: 'grab'\n          },\n          children: node.label\n        }, node.type, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 25\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 9\n  }, this);\n};\n_s(PipelineToolbar, \"mJOq52LKVWj4vGR2hEwZVHWsLAc=\");\n_c = PipelineToolbar;\nvar _c;\n$RefreshReg$(_c, \"PipelineToolbar\");", "map": {"version": 3, "names": ["useState", "useEffect", "jsxDEV", "_jsxDEV", "PipelineToolbar", "_s", "screenWidth", "setScreenWidth", "window", "innerWidth", "handleResize", "addEventListener", "removeEventListener", "getResponsiveTitle", "onDragStart", "event", "nodeType", "appData", "target", "style", "cursor", "dataTransfer", "setData", "JSON", "stringify", "effectAllowed", "onDragEnd", "nodeTypes", "type", "label", "color", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "node", "draggable", "_c", "$RefreshReg$"], "sources": ["D:/Job Assesment/frontend_technical_assessment/frontend/src/toolbar.js"], "sourcesContent": ["// toolbar.js\nimport { useState, useEffect } from 'react';\n\nexport const PipelineToolbar = () => {\n    const [screenWidth, setScreenWidth] = useState(window.innerWidth);\n\n    useEffect(() => {\n        const handleResize = () => setScreenWidth(window.innerWidth);\n        window.addEventListener('resize', handleResize);\n        return () => window.removeEventListener('resize', handleResize);\n    }, []);\n\n    const getResponsiveTitle = () => {\n        if (screenWidth <= 360) return 'VectorShift';\n        if (screenWidth <= 480) return 'VectorShift Builder';\n        return 'VectorShift Pipeline Builder';\n    };\n    const onDragStart = (event, nodeType) => {\n        const appData = { nodeType };\n        event.target.style.cursor = 'grabbing';\n        event.dataTransfer.setData('application/reactflow', JSON.stringify(appData));\n        event.dataTransfer.effectAllowed = 'move';\n    };\n\n    const onDragEnd = (event) => {\n        event.target.style.cursor = 'grab';\n    };\n\n    const nodeTypes = [\n        { type: 'customInput', label: 'Input', color: '#8b5cf6' },\n        { type: 'llm', label: 'LLM', color: '#6366f1' },\n        { type: 'customOutput', label: 'Output', color: '#8b5cf6' },\n        { type: 'text', label: 'Text', color: '#10b981' },\n        { type: 'math', label: 'Math', color: '#3b82f6' },\n        { type: 'filter', label: 'Filter', color: '#f59e0b' },\n        { type: 'timer', label: 'Timer', color: '#ef4444' },\n        { type: 'switch', label: 'Switch', color: '#06b6d4' },\n        { type: 'aggregator', label: 'Aggregator', color: '#ec4899' }\n    ];\n\n    return (\n        <div className=\"pipeline-node-toolbar\">\n            <div className=\"node-toolbar-content\">\n                <div className=\"toolbar-title\">\n                    <span>{getResponsiveTitle()}</span>\n                </div>\n                <div className=\"toolbar-nodes\">\n                    {nodeTypes.map((node) => (\n                        <div\n                            key={node.type}\n                            className=\"draggable-node\"\n                            draggable\n                            onDragStart={(event) => onDragStart(event, node.type)}\n                            onDragEnd={onDragEnd}\n                            style={{\n                                cursor: 'grab'\n                            }}\n                        >\n                            {node.label}\n                        </div>\n                    ))}\n                </div>\n            </div>\n        </div>\n    );\n};\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,OAAO,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGP,QAAQ,CAACQ,MAAM,CAACC,UAAU,CAAC;EAEjER,SAAS,CAAC,MAAM;IACZ,MAAMS,YAAY,GAAGA,CAAA,KAAMH,cAAc,CAACC,MAAM,CAACC,UAAU,CAAC;IAC5DD,MAAM,CAACG,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMF,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,kBAAkB,GAAGA,CAAA,KAAM;IAC7B,IAAIP,WAAW,IAAI,GAAG,EAAE,OAAO,aAAa;IAC5C,IAAIA,WAAW,IAAI,GAAG,EAAE,OAAO,qBAAqB;IACpD,OAAO,8BAA8B;EACzC,CAAC;EACD,MAAMQ,WAAW,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IACrC,MAAMC,OAAO,GAAG;MAAED;IAAS,CAAC;IAC5BD,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,UAAU;IACtCL,KAAK,CAACM,YAAY,CAACC,OAAO,CAAC,uBAAuB,EAAEC,IAAI,CAACC,SAAS,CAACP,OAAO,CAAC,CAAC;IAC5EF,KAAK,CAACM,YAAY,CAACI,aAAa,GAAG,MAAM;EAC7C,CAAC;EAED,MAAMC,SAAS,GAAIX,KAAK,IAAK;IACzBA,KAAK,CAACG,MAAM,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;EACtC,CAAC;EAED,MAAMO,SAAS,GAAG,CACd;IAAEC,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,EACzD;IAAEF,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC/C;IAAEF,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EAC3D;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,IAAI,EAAE,MAAM;IAAEC,KAAK,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAU,CAAC,EACjD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrD;IAAEF,IAAI,EAAE,OAAO;IAAEC,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAU,CAAC,EACnD;IAAEF,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrD;IAAEF,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAU,CAAC,CAChE;EAED,oBACI3B,OAAA;IAAK4B,SAAS,EAAC,uBAAuB;IAAAC,QAAA,eAClC7B,OAAA;MAAK4B,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBACjC7B,OAAA;QAAK4B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC1B7B,OAAA;UAAA6B,QAAA,EAAOnB,kBAAkB,CAAC;QAAC;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACNjC,OAAA;QAAK4B,SAAS,EAAC,eAAe;QAAAC,QAAA,EACzBL,SAAS,CAACU,GAAG,CAAEC,IAAI,iBAChBnC,OAAA;UAEI4B,SAAS,EAAC,gBAAgB;UAC1BQ,SAAS;UACTzB,WAAW,EAAGC,KAAK,IAAKD,WAAW,CAACC,KAAK,EAAEuB,IAAI,CAACV,IAAI,CAAE;UACtDF,SAAS,EAAEA,SAAU;UACrBP,KAAK,EAAE;YACHC,MAAM,EAAE;UACZ,CAAE;UAAAY,QAAA,EAEDM,IAAI,CAACT;QAAK,GATNS,IAAI,CAACV,IAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUb,CACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC/B,EAAA,CA9DWD,eAAe;AAAAoC,EAAA,GAAfpC,eAAe;AAAA,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}