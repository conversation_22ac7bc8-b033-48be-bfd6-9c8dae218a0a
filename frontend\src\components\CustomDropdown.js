// CustomDropdown.js
// Custom dropdown component with smooth animations and custom arrow

import { useState, useRef, useEffect } from 'react';

export const CustomDropdown = ({ 
  value, 
  onChange, 
  options = [], 
  placeholder = "Select option",
  className = "",
  style = {},
  disabled = false 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [focusedIndex, setFocusedIndex] = useState(-1);
  const dropdownRef = useRef(null);
  const optionsRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setFocusedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          setFocusedIndex(prev => 
            prev < options.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          setFocusedIndex(prev => 
            prev > 0 ? prev - 1 : options.length - 1
          );
          break;
        case 'Enter':
          event.preventDefault();
          if (focusedIndex >= 0) {
            handleOptionSelect(options[focusedIndex]);
          }
          break;
        case 'Escape':
          setIsOpen(false);
          setFocusedIndex(-1);
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, focusedIndex, options]);

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      setFocusedIndex(-1);
    }
  };

  const handleOptionSelect = (option) => {
    onChange({ target: { value: option.value } });
    setIsOpen(false);
    setFocusedIndex(-1);
  };

  const selectedOption = options.find(opt => opt.value === value);
  const displayText = selectedOption ? selectedOption.label : placeholder;

  return (
    <div 
      ref={dropdownRef}
      className={`custom-dropdown ${className} ${isOpen ? 'open' : ''} ${disabled ? 'disabled' : ''}`}
      style={style}
    >
      <div 
        className="dropdown-trigger"
        onClick={handleToggle}
        tabIndex={disabled ? -1 : 0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleToggle();
          }
        }}
      >
        <span className="dropdown-text">{displayText}</span>
        <div className={`dropdown-arrow ${isOpen ? 'rotated' : ''}`}>
          <svg width="12" height="8" viewBox="0 0 12 8" fill="none">
            <path 
              d="M1 1.5L6 6.5L11 1.5" 
              stroke="currentColor" 
              strokeWidth="1.5" 
              strokeLinecap="round" 
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
      
      <div 
        ref={optionsRef}
        className={`dropdown-options ${isOpen ? 'visible' : ''}`}
      >
        {options.map((option, index) => (
          <div
            key={option.value}
            className={`dropdown-option ${
              option.value === value ? 'selected' : ''
            } ${
              index === focusedIndex ? 'focused' : ''
            }`}
            onClick={() => handleOptionSelect(option)}
            onMouseEnter={() => setFocusedIndex(index)}
          >
            {option.label}
          </div>
        ))}
      </div>
    </div>
  );
};
